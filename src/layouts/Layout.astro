---
import "@styles/global.css";

export interface Props {
  title?: string;
  description?: string;
}

const {
  title = "Plattenleger Basel - Professionelle Fliesenleger & Plattenverlegung",
  description = "Ihr Experte für Plattenverlegung in Basel. Badezimmer, Küchen, Pools - professionell und zuverlässig. Kostenlose Beratung und Offerte.",
} = Astro.props;
---

<!doctype html>
<html data-theme="corporate" lang="de">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content={description} />
    <meta
      name="keywords"
      content="Plattenleger Basel, Fliesenleger Basel, Badezimmer Renovation, Küche Fliesen, Pool Fliesen, Plattenverlegung"
    />
    <meta name="author" content="Plattenleger Basel" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:locale" content="de_CH" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />

    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <title>{title}</title>
  </head>
  <body>
    <slot />
  </body>
</html>
