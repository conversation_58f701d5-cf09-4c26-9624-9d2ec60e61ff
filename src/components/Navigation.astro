---
import { Phone, Menu, X } from "@lucide/astro";

const phoneNumber = import.meta.env.PUBLIC_PHONE_NUMBER;
const phoneDisplay = import.meta.env.PUBLIC_PHONE_DISPLAY;
const businessName = import.meta.env.PUBLIC_BUSINESS_NAME;
---

<nav class="navbar bg-base-100 shadow-lg sticky top-0 z-50">
  <div class="navbar-start">
    <div class="dropdown">
      <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
        <Menu size={24} />
      </div>
      <ul
        tabindex="0"
        class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow"
      >
        <li><a href="/">Home</a></li>
        <li><a href="/services">Leistungen</a></li>
        <li>
          <a href={`tel:${phoneNumber}`} class="text-secondary font-semibold">
            <Phone size={16} />
            {phoneDisplay}
          </a>
        </li>
      </ul>
    </div>
    <a href="/" class="btn btn-ghost text-xl font-bold"> {businessName} </a>
  </div>

  <div class="navbar-center hidden lg:flex">
    <ul class="menu menu-horizontal px-1">
      <li><a href="/" class="text-lg">Home</a></li>
      <li><a href="/services" class="text-lg">Leistungen</a></li>
    </ul>
  </div>

  <div class="navbar-end">
    <a href={`tel:${phoneNumber}`} class="btn btn-secondary hidden lg:flex">
      <Phone size={20} />
      {phoneDisplay}
    </a>
  </div>
</nav>
