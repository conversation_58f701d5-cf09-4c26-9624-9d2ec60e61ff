---
import type { AstroComponent } from "@lucide/astro";

export interface Props {
  title: string;
  description: string;
  features: string[];
  icon: AstroComponent;
}

const { title, description, features, icon: Icon } = Astro.props;
---

<div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
  <figure class="px-6 pt-6">
    <div
      class={`w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center`}
    >
      <Icon size={32} class="text-primary" />
    </div>
  </figure>
  <div class="card-body text-center">
    <h3 class="card-title justify-center text-2xl">{title}</h3>
    <p class="text-base-content font-medium">
      {description}
    </p>
    <ul class="text-sm text-left mt-4 space-y-1">
      {
        features.map((feature) => (
          <li class="flex items-start gap-2">
            <span class="text-primary font-bold flex-shrink-0 mt-0.5">✓</span>
            <span>{feature}</span>
          </li>
        ))
      }
    </ul>
  </div>
</div>
