---
import { Phone, Mail, MapPin, Clock } from "@lucide/astro";
---

<section id="contact" class="py-16 bg-base-100">
  <div class="container mx-auto px-4">
    <div class="text-center mb-12">
      <h2 class="text-4xl font-bold mb-4">Kostenlose Offerte anfordern</h2>
      <p class="text-xl text-base-content/70 max-w-2xl mx-auto">
        Beschreiben Sie Ihr Projekt und erhalten Sie innerhalb von 24 Stunden
        eine unverbindliche Offerte
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
      <!-- Contact Form -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-2xl mb-6">Projekt beschreiben</h3>
          <form class="space-y-4" id="contactForm" action="#" method="POST">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-semibold">Vorname *</span>
                </label>
                <input
                  type="text"
                  name="firstName"
                  placeholder="Ihr Vorname"
                  class="input input-bordered"
                  required
                />
              </div>
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-semibold">Nachname *</span>
                </label>
                <input
                  type="text"
                  name="lastName"
                  placeholder="Ihr Nachname"
                  class="input input-bordered"
                  required
                />
              </div>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-semibold">E-Mail *</span>
              </label>
              <input
                type="email"
                name="email"
                placeholder="<EMAIL>"
                class="input input-bordered"
                required
              />
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-semibold">Telefon *</span>
              </label>
              <input
                type="tel"
                name="phone"
                placeholder="+41 XX XXX XX XX"
                class="input input-bordered"
                required
              />
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-semibold">Art der Arbeit</span>
              </label>
              <select name="serviceType" class="select select-bordered">
                <option disabled selected>Wählen Sie eine Option</option>
                <option value="badezimmer">Badezimmer</option>
                <option value="kueche">Küche</option>
                <option value="pool">Pool & Wellness</option>
                <option value="wohnbereich">Wohnbereich</option>
                <option value="gewerbe">Gewerbebau</option>
                <option value="aussenbereich">Aussenbereich</option>
                <option value="sonstiges">Sonstiges</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-semibold">Projektbeschreibung</span
                >
              </label>
              <textarea
                name="message"
                placeholder="Beschreiben Sie Ihr Projekt: Welche Räume? Welche Fläche? Besondere Wünsche?"
                class="textarea textarea-bordered h-32"></textarea>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-semibold"
                  >Gewünschter Zeitrahmen</span
                >
              </label>
              <select name="timeframe" class="select select-bordered">
                <option disabled selected>Wann soll das Projekt starten?</option
                >
                <option value="sofort">So schnell wie möglich</option>
                <option value="1-monat">In den nächsten 4 Wochen</option>
                <option value="3-monate">In den nächsten 3 Monaten</option>
                <option value="6-monate">In den nächsten 6 Monaten</option>
                <option value="planung">Noch in der Planungsphase</option>
              </select>
            </div>

            <div class="form-control mt-6">
              <button type="submit" class="btn btn-primary btn-lg">
                <Mail size={20} />
                Kostenlose Offerte anfordern
              </button>
            </div>

            <p class="text-sm text-base-content/60 mt-4">
              * Pflichtfelder. Ihre Daten werden vertraulich behandelt und nicht
              an Dritte weitergegeben.
            </p>
          </form>
        </div>
      </div>

      <!-- Contact Information -->
      <div class="space-y-8">
        <div class="card bg-primary text-primary-content shadow-xl">
          <div class="card-body">
            <h3 class="card-title text-2xl mb-4">Direkter Kontakt</h3>
            <div class="space-y-4">
              <div class="flex items-center gap-3">
                <Phone size={24} />
                <div>
                  <p class="font-semibold">Telefon</p>
                  <a href="tel:+41612345678" class="text-lg hover:underline"
                    >061 234 56 78</a
                  >
                </div>
              </div>
              <div class="flex items-center gap-3">
                <Mail size={24} />
                <div>
                  <p class="font-semibold">E-Mail</p>
                  <a
                    href="mailto:<EMAIL>"
                    class="hover:underline"><EMAIL></a
                  >
                </div>
              </div>
              <div class="flex items-center gap-3">
                <MapPin size={24} />
                <div>
                  <p class="font-semibold">Servicegebiet</p>
                  <p>Basel und Umgebung (30km)</p>
                </div>
              </div>
              <div class="flex items-center gap-3">
                <Clock size={24} />
                <div>
                  <p class="font-semibold">Öffnungszeiten</p>
                  <p>Mo-Fr: 07:00 - 18:00<br />Sa: 08:00 - 16:00</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="card bg-base-200 shadow-xl">
          <div class="card-body">
            <h3 class="card-title text-xl mb-4">Warum uns wählen?</h3>
            <ul class="space-y-3">
              <li class="flex items-start gap-2">
                <span class="text-primary font-bold">✓</span>
                <span
                  ><strong>15+ Jahre Erfahrung</strong> in der Region Basel</span
                >
              </li>
              <li class="flex items-start gap-2">
                <span class="text-primary font-bold">✓</span>
                <span><strong>Kostenlose Beratung</strong> und Offerte</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-primary font-bold">✓</span>
                <span><strong>Faire Preise</strong> ohne versteckte Kosten</span
                >
              </li>
              <li class="flex items-start gap-2">
                <span class="text-primary font-bold">✓</span>
                <span><strong>Termingerechte Ausführung</strong></span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-primary font-bold">✓</span>
                <span><strong>Garantie</strong> auf alle Arbeiten</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-primary font-bold">✓</span>
                <span><strong>Saubere Arbeitsweise</strong></span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const form = document.getElementById("contactForm");
    if (form) {
      form.addEventListener("submit", function (e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // Simple validation
        if (!data.firstName || !data.lastName || !data.email || !data.phone) {
          alert("Bitte füllen Sie alle Pflichtfelder aus.");
          return;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
          alert("Bitte geben Sie eine gültige E-Mail-Adresse ein.");
          return;
        }

        // Show success message (in a real implementation, you would send this to a server)
        alert(
          "Vielen Dank für Ihre Anfrage! Wir melden uns innerhalb von 24 Stunden bei Ihnen.",
        );

        // Reset form
        form.reset();

        // In a real implementation, you would send the data to your server:
        // fetch('/api/contact', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(data)
        // });
      });
    }
  });
</script>
