---
import { Bath, ChefHat, Waves, Home, Building, Sparkles } from "@lucide/astro";
import ServiceCard from "./ServiceCard.astro";

const services = [
  {
    title: "Badez<PERSON>",
    description:
      "Komplette Badsanierung mit hochwertigen Fliesen. Wasserdichte Verlegung für langanhaltende Schönheit.",
    features: [
      "Wandfliesen & Bodenfliesen",
      "Duschabtrennungen",
      "Wasserdichte Abdichtung",
      "Moderne Designs",
    ],
    icon: Bath,
  },
  {
    title: "<PERSON>ü<PERSON>",
    description:
      "Stilvolle Küchenrückwände und Arbeitsplatten. Pflegeleichte und hygienische Lösungen.",
    features: [
      "Küchenrückwände",
      "Arbeitsplatten",
      "Spritzsch<PERSON>",
      "Pflegeleichte Oberflächen",
    ],
    icon: ChefHat,
  },
  {
    title: "Pools & Wellness",
    description:
      "Spezialisiert auf Schwimmbäder und Wellnessbereiche. Rutschfeste und chlorbeständige Materialien.",
    features: [
      "Poolbecken",
      "Poolumrandung",
      "Saunen & Dampfbäder",
      "Rutschfeste Fliesen",
    ],
    icon: Waves,
  },
  {
    title: "Wohnberei<PERSON>",
    description:
      "Elegante Bodenbeläge für Wohn- und Schlafräume. Warme und gemütliche Atmosphäre.",
    features: [
      "Wohnzimmer",
      "Schlafzimmer",
      "Flure & Eingänge",
      "Fußbodenheizung geeignet",
    ],
    icon: Home,
  },
  {
    title: "Gewerbebau",
    description:
      "Robuste Lösungen für Geschäfte, Büros und öffentliche Gebäude. Strapazierfähig und repräsentativ.",
    features: [
      "Bürogebäude",
      "Geschäfte & Restaurants",
      "Öffentliche Gebäude",
      "Industrieböden",
    ],
    icon: Building,
  },
  {
    title: "Aussenbereiche",
    description:
      "Wetterbeständige Terrassen und Balkone. Frostfeste Materialien für alle Jahreszeiten.",
    features: [
      "Terrassen",
      "Balkone",
      "Eingangsbereiche",
      "Frostfeste Fliesen",
    ],
    icon: Sparkles,
  },
];
---

<section class="py-16 bg-base-200">
  <div class="container mx-auto px-4">
    <div class="text-center mb-12">
      <h2 class="text-4xl font-bold mb-4">Unsere Leistungen</h2>
      <p class="text-xl text-base-content/70 max-w-2xl mx-auto">
        Professionelle Plattenverlegung für alle Bereiche - von der Planung bis
        zur perfekten Ausführung
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {
        services.map((service) => (
          <ServiceCard
            title={service.title}
            description={service.description}
            features={service.features}
            icon={service.icon}
          />
        ))
      }
    </div>

    <div class="text-center mt-12">
      <a href="/services" class="btn btn-primary btn-lg">
        Alle Leistungen ansehen
      </a>
    </div>
  </div>
</section>
