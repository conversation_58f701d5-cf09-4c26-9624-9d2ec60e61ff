---
import heroImage from "@assets/hero-background.jpg";
import { Phone, Star } from "@lucide/astro";
import { Image } from "astro:assets";

const phoneNumber = import.meta.env.PUBLIC_PHONE_NUMBER;
const phoneDisplay = import.meta.env.PUBLIC_PHONE_DISPLAY;
---

<div class="hero h-[70vh] min-h-[500px] overflow-hidden">
  <Image
    src={heroImage}
    alt="Professionelle Plattenverlegung in Basel"
    class="object-cover w-full h-[70vh]"
  />
  <div class="hero-overlay bg-opacity-60"></div>
  <div class="hero-content text-neutral-content text-center max-w-4xl">
    <div class="bg-neutral/80 p-8 rounded-xl backdrop-blur-sm">
      <div class="flex justify-center mb-4">
        <div class="flex gap-1">
          {
            Array.from({ length: 5 }).map(() => (
              <Star size={20} class="fill-yellow-400 text-yellow-400" />
            ))
          }
        </div>
      </div>
      <h1 class="mb-6 text-3xl sm:text-5xl lg:text-6xl font-bold">
        Ihre Experten für<br />
        <span class="text-primary">Plattenverlegung</span> in Basel
      </h1>
      <p class="mb-8 text-lg sm:text-xl max-w-2xl mx-auto">
        Professionelle Fliesenleger für Badezimmer, Küchen, Pools und alle
        Innen- & Aussenbereiche in der Region Basel.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href={`tel:${phoneNumber}`} class="btn btn-secondary btn-lg">
          <Phone size={20} />
          {phoneDisplay}
        </a>
        <a href="#contact" class="btn btn-primary btn-lg">
          Kostenlose Offerte
        </a>
      </div>
      <p class="mt-4 text-sm opacity-90">
        ✓&nbsp;Kostenlose&nbsp;Beratung ✓&nbsp;Faire&nbsp;Preise
        ✓&nbsp;Termingerechte&nbsp;Ausführung
      </p>
    </div>
  </div>
</div>
