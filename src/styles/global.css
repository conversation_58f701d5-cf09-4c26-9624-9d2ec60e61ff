@import "tailwindcss";
@plugin "daisyui" {
  themes:
    corporate --default,
    business,
    winter;
}

/* High contrast theme optimizations for tiling service accessibility */
@plugin "daisyui/theme" {
  name: "corporate";

  /* High contrast primary color for better readability */
  --color-primary: oklch(45% 0.2 220); /* Darker, more saturated blue */
  --color-primary-content: oklch(100% 0 0); /* Pure white text */

  /* High contrast secondary for craftsmanship appeal */
  --color-secondary: oklch(50% 0.25 35); /* Darker orange-brown */
  --color-secondary-content: oklch(100% 0 0); /* Pure white text */

  /* High contrast accent */
  --color-accent: oklch(40% 0.2 160); /* Darker teal */
  --color-accent-content: oklch(100% 0 0); /* Pure white text */

  /* Enhanced base colors for better contrast */
  --color-base-100: oklch(100% 0 0); /* Pure white background */
  --color-base-200: oklch(96% 0 0); /* Very light gray */
  --color-base-300: oklch(92% 0 0); /* Light gray */
  --color-base-content: oklch(15% 0 0); /* Very dark text */

  /* Neutral colors with high contrast */
  --color-neutral: oklch(20% 0 0); /* Very dark for footer/headers */
  --color-neutral-content: oklch(100% 0 0); /* Pure white text */

  /* Success, warning, error with better contrast */
  --color-success: oklch(45% 0.2 140); /* Darker green */
  --color-success-content: oklch(100% 0 0);
  --color-warning: oklch(55% 0.25 70); /* Darker yellow */
  --color-warning-content: oklch(15% 0 0); /* Dark text on yellow */
  --color-error: oklch(50% 0.25 25); /* Darker red */
  --color-error-content: oklch(100% 0 0);
}

/* Additional accessibility improvements */
:root {
  /* Ensure minimum contrast ratios */
  --text-opacity-high: 1;
  --text-opacity-medium: 0.9;
  --text-opacity-low: 0.8;
}

/* Enhanced text contrast classes */
.text-high-contrast {
  color: oklch(10% 0 0) !important; /* Nearly black text */
}

.bg-high-contrast {
  background-color: oklch(100% 0 0) !important; /* Pure white background */
}

/* Button contrast improvements */
.btn-primary {
  --tw-bg-opacity: 1;
  background-color: oklch(40% 0.2 220);
  border-color: oklch(40% 0.2 220);
  color: oklch(100% 0 0);
}

.btn-primary:hover {
  background-color: oklch(35% 0.2 220);
  border-color: oklch(35% 0.2 220);
}

.btn-secondary {
  --tw-bg-opacity: 1;
  background-color: oklch(45% 0.25 35);
  border-color: oklch(45% 0.25 35);
  color: oklch(100% 0 0);
}

.btn-secondary:hover {
  background-color: oklch(40% 0.25 35);
  border-color: oklch(40% 0.25 35);
}
