# Plattenleger Basel - Lead Generation Website

A high-converting lead generation website for a tile layer/flooring specialist business in Basel, Switzerland.

## 🎯 Purpose

This website is designed to generate leads for "Plattenleger in Basel" through Google Ads and organic search. The focus is on conversion optimization with clear calls-to-action and easy contact methods.

## 🚀 Features

### Pages

- **Homepage**: Hero section, services overview, benefits, contact form
- **Services Page**: Detailed service descriptions with images and benefits
- **Navigation**: Responsive navigation with phone number prominently displayed

### Components

- **Hero Section**: Reduced height, conversion-focused with phone CTA and quote request
- **Services Overview**: 6 main service categories with icons and descriptions
- **Benefits Section**: Why choose us, testimonials, trust indicators
- **Contact Form**: Lead capture form with validation for free estimates
- **Navigation**: Mobile-responsive with hamburger menu
- **Footer**: Complete contact information and links

### Key Conversion Elements

- ⭐ 5-star rating display in hero
- 📞 Phone number prominently displayed (clickable)
- 💬 Multiple contact methods (phone, form, email)
- ✅ Trust indicators (15+ years experience, 500+ projects, guarantees)
- 🎯 Clear value propositions
- 📝 Lead capture form with project details
- 💯 Customer testimonials
- 🔒 Professional design with DaisyUI components

## 🛠 Technology Stack

- **Astro 5.8.0**: Static site generator
- **Tailwind CSS 4.1.4**: Utility-first CSS framework
- **DaisyUI 5.0.40**: Component library for Tailwind
- **Lucide Icons**: Modern icon library
- **TypeScript**: Type safety

## 🏃‍♂️ Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Development

The site will be available at `http://localhost:4321/`

## 📱 Responsive Design

The website is fully responsive and optimized for:

- Desktop (1024px+)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🎨 Design Features

### Color Scheme

- Primary: DaisyUI Nord theme
- Professional blue/gray palette
- High contrast for accessibility

### Typography

- Clear hierarchy with proper heading structure
- German language content
- SEO-optimized titles and descriptions

### Layout

- Clean, professional design
- Card-based components
- Consistent spacing and alignment
- Smooth hover effects and transitions

## 📞 Contact Information

The website includes multiple contact methods:

- **Phone**: 061 234 56 78 (clickable links)
- **Email**: <EMAIL>
- **Service Area**: Basel and surrounding area (30km radius)
- **Hours**: Mon-Fri 07:00-18:00, Sat 08:00-16:00

## 🔧 Customization

### Adding New Services

1. Update the services data in `src/components/Services.astro`
2. Add corresponding icons from Lucide
3. Update the services page with detailed descriptions

### Modifying Contact Information

1. Update phone numbers in all components
2. Change email addresses
3. Modify service area and hours

### Styling Changes

1. Modify the DaisyUI theme in `src/styles/global.css`
2. Adjust Tailwind classes in components
3. Update color schemes and typography

## 📈 SEO Optimization

- German language meta tags
- Proper heading structure (H1, H2, H3)
- Descriptive alt texts for images
- Local SEO keywords for Basel region
- Open Graph and Twitter meta tags
- Semantic HTML structure

## 🚀 Deployment

The site can be deployed to any static hosting service:

- Netlify
- Vercel
- GitHub Pages
- AWS S3 + CloudFront

Build command: `npm run build`
Output directory: `dist/`

## 📝 Form Handling

The contact form includes:

- Client-side validation
- Required field checking
- Email format validation
- Success/error messaging
- Form reset after submission

For production, integrate with:

- Email services (SendGrid, Mailgun)
- CRM systems
- Lead management tools

## 🎯 Conversion Optimization

The site includes several conversion optimization features:

- Above-the-fold phone number
- Multiple contact methods
- Social proof (testimonials, ratings)
- Clear value propositions
- Urgency indicators
- Professional trust signals
- Easy-to-use contact form
- Mobile-optimized design

## 📊 Analytics

Consider adding:

- Google Analytics 4
- Google Tag Manager
- Facebook Pixel (for ads)
- Call tracking numbers
- Form submission tracking
- Conversion goal setup
