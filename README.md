# Basler Plattenleger - Lead Generation Website

A high-converting lead generation website for a tile layer/flooring specialist business in Basel, Switzerland.

## 🎯 Purpose

This website is designed to generate leads for "Plattenleger in Basel" through Google Ads and organic search. The focus is on conversion optimization with clear calls-to-action and easy contact methods.

## 🚀 Features

### Pages

- **Homepage**: Hero section, services overview, benefits, contact form
- **Services Page**: Detailed service descriptions with images and benefits
- **Navigation**: Responsive navigation with phone number prominently displayed

### Components

- **Hero Section**: Reduced height, conversion-focused with phone CTA and quote request
- **Services Overview**: 6 main service categories with icons and descriptions
- **Benefits Section**: Why choose us, testimonials, trust indicators
- **Contact Form**: Lead capture form with validation for free estimates
- **Navigation**: Mobile-responsive with hamburger menu
- **Footer**: Complete contact information and links

### Key Conversion Elements

- ⭐ 5-star rating display in hero
- 📞 Phone number prominently displayed (clickable)
- 💬 Multiple contact methods (phone, form, email)
- ✅ Trust indicators (15+ years experience, 500+ projects, guarantees)
- 🎯 Clear value propositions
- 📝 Lead capture form with project details
- 💯 Customer testimonials
- 🔒 Professional design with DaisyUI components

## 🛠 Technology Stack

- **Astro 5.8.0**: Static site generator
- **Tailwind CSS 4.1.4**: Utility-first CSS framework
- **DaisyUI 5.0.40**: Component library for Tailwind
- **Lucide Icons**: Modern icon library
- **TypeScript**: Type safety

## 🏃‍♂️ Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Development

The site will be available at `http://localhost:4321/`

## 📱 Responsive Design

The website is fully responsive and optimized for:

- Desktop (1024px+)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🎨 Design Features

### Color Scheme

- **Base Theme**: DaisyUI Corporate theme (high contrast)
- **Primary**: Dark professional blue (45% lightness) for maximum readability
- **Secondary**: Warm orange-brown for craftsmanship appeal
- **Accent**: Professional teal for highlights
- **Background**: Pure white (#FFFFFF) for maximum contrast
- **Text**: Very dark (15% lightness) for excellent readability
- **Optimized for**: Older customers, accessibility, tiling/construction industry
- **Contrast Ratio**: WCAG AA compliant for all text elements

### Typography

- Clear hierarchy with proper heading structure
- German language content
- SEO-optimized titles and descriptions

### Layout

- Clean, professional design
- Card-based components
- Consistent spacing and alignment
- Smooth hover effects and transitions

## 📞 Contact Information

The website includes multiple contact methods:

- **Phone**: 061 234 56 78 (clickable links)
- **Email**: <EMAIL>
- **Service Area**: Basel and surrounding area (30km radius)
- **Hours**: Mon-Fri 07:00-18:00, Sat 08:00-16:00

## 🔧 Customization

### Adding New Services

The services are now managed through a reusable component system:

1. **ServiceCard Component**: `src/components/ServiceCard.astro` - Reusable card component
2. **Services Data**: Located in `src/components/Services.astro` as a structured array
3. **Adding New Services**:
   ```javascript
   {
     title: "Service Name",
     description: "Service description text",
     features: ["Feature 1", "Feature 2", "Feature 3"],
     icon: IconComponent, // From Lucide
     iconColor: "primary" | "secondary" | "accent"
   }
   ```
4. **Icon Colors**: Use different colors for visual variety (primary, secondary, accent)
5. **Features**: Array of service features displayed as checkmarks

### Modifying Contact Information

All contact information is now managed through environment variables in the `.env` file:

1. **Phone Number**: Update `PUBLIC_PHONE_NUMBER` and `PUBLIC_PHONE_DISPLAY`
2. **Email**: Update `PUBLIC_EMAIL`
3. **Business Name**: Update `PUBLIC_BUSINESS_NAME`
4. **Service Area**: Update `PUBLIC_SERVICE_AREA`
5. **Hours**: Update `PUBLIC_HOURS_WEEKDAY` and `PUBLIC_HOURS_SATURDAY`

Copy `.env.example` to `.env` and customize the values for your business.

### Styling Changes

1. Modify the DaisyUI theme in `src/styles/global.css`
2. Adjust Tailwind classes in components
3. Update color schemes and typography

## 📈 SEO Optimization

- German language meta tags
- Proper heading structure (H1, H2, H3)
- Descriptive alt texts for images
- Local SEO keywords for Basel region
- Open Graph and Twitter meta tags
- Semantic HTML structure

## 🚀 Deployment

The site can be deployed to any static hosting service:

- Netlify
- Vercel
- GitHub Pages
- AWS S3 + CloudFront

Build command: `npm run build`
Output directory: `dist/`

## 🔧 Environment Variables

The site uses environment variables for easy configuration. All contact information and business details are managed through the `.env` file.

### Required Variables

Copy `.env.example` to `.env` and update the following variables:

```bash
# Contact Information
PUBLIC_PHONE_NUMBER=+***********          # Phone number for tel: links
PUBLIC_PHONE_DISPLAY=061 234 56 78        # Formatted phone for display
PUBLIC_EMAIL=<EMAIL>   # Contact email address

# Business Information
PUBLIC_BUSINESS_NAME=Plattenleger Basel   # Business name
PUBLIC_SERVICE_AREA=Basel und Umgebung (30km)  # Service area description
PUBLIC_HOURS_WEEKDAY=Mo-Fr: 07:00 - 18:00      # Weekday hours
PUBLIC_HOURS_SATURDAY=Sa: 08:00 - 16:00        # Saturday hours
```

### Usage

Environment variables are automatically loaded by Astro and can be accessed in components using `import.meta.env.PUBLIC_*`. All variables used in the browser must be prefixed with `PUBLIC_`.

### Deployment

When deploying to hosting services, make sure to set these environment variables in your deployment platform:

- **Netlify**: Site settings → Environment variables
- **Vercel**: Project settings → Environment Variables
- **GitHub Pages**: Repository settings → Secrets and variables

## 📝 Form Handling

The contact form includes:

- Client-side validation
- Required field checking
- Email format validation
- Success/error messaging
- Form reset after submission

For production, integrate with:

- Email services (SendGrid, Mailgun)
- CRM systems
- Lead management tools

## 🎯 Conversion Optimization

The site includes several conversion optimization features:

- Above-the-fold phone number
- Multiple contact methods
- Social proof (testimonials, ratings)
- Clear value propositions
- Urgency indicators
- Professional trust signals
- Easy-to-use contact form
- Mobile-optimized design

## 📊 Analytics

Consider adding:

- Google Analytics 4
- Google Tag Manager
- Facebook Pixel (for ads)
- Call tracking numbers
- Form submission tracking
- Conversion goal setup
