{"name": "basler-plattenleger", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@lucide/astro": "^0.511.0", "@tailwindcss/vite": "^4.1.4", "astro": "^5.8.0", "daisyui": "^5.0.40", "tailwindcss": "^4.1.4"}, "devDependencies": {"prettier": "3.5.3", "prettier-plugin-astro": "0.14.1", "prettier-plugin-tailwindcss": "0.6.11"}, "prettier": {"plugins": ["prettier-plugin-tailwindcss", "prettier-plugin-astro"], "overrides": [{"files": "*.astro", "options": {"parser": "astro"}}]}}